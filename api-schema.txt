Rencana schema buat API endpoints:

{
  "endpoints": {
    "authentication": {
      "register": {
        "method": "POST",
        "url": "/api/auth/register",
        "body": {
          "username": "string",
          "email": "string",
          "password": "string"
        },
        "response": {
          "user": {
            "id": "string",
            "username": "string",
            "email": "string",
            "createdAt": "timestamp"
          },
          "token": "string"
        }
      },
      "login": {
        "method": "POST",
        "url": "/api/auth/login",
        "body": {
          "email": "string",
          "password": "string"
        },
        "response": {
          "user": {
            "id": "string",
            "username": "string",
            "email": "string"
          },
          "token": "string"
        }
      },
      "logout": {
        "method": "POST", 
        "url": "/api/auth/logout",
        "auth": "required"
      },
      "getUser": {
        "method": "GET", 
        "url": "/api/auth/user",
        "auth": "required",
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "createdAt": "timestamp"
        }
      },
      "refreshToken": {
        "method": "POST",
        "url": "/api/auth/refresh",
        "body": {
          "refreshToken": "string"
        },
        "response": {
          "token": "string",
          "refreshToken": "string"
        }
      }
    },
    "posts": {
      "getAllPosts": {
        "method": "GET", 
        "url": "/api/posts",
        "query": {
          "page": "number",
          "limit": "number",
          "sort": "string"
        }
      },
      "getPostById": {"method": "GET", "url": "/api/posts/{id}"},
      "createPost": {
        "method": "POST",
        "url": "/api/posts",
        "auth": "required",
        "body": {
          "title": "string",
          "content": "string",
          "image": "application/json"
        },
        "response": {
          "id": "string",
          "title": "string",
          "content": "string",
          "imageUrl": "string",
          "userId": "string",
          "createdAt": "timestamp"
        }
      },
      "updatePost": {
        "method": "PUT",
        "url": "/api/posts/{id}",
        "auth": "required",
        "ownerOnly": true,
        "body": {
          "title": "string",
          "content": "string",
          "image": "application/json"
        }
      },
      "deletePost": {
        "method": "DELETE", 
        "url": "/api/posts/{id}",
        "auth": "required",
        "ownerOnly": true
      }
    },
    "comments": {
      "getComments": {"method": "GET", "url": "/api/posts/{id}/comments"},
      "createComment": {
        "method": "POST",
        "url": "/api/posts/{id}/comments",
        "auth": "required",
        "body": {
          "content": "string"
        },
        "response": {
          "id": "string",
          "content": "string",
          "userId": "string",
          "postId": "string",
          "createdAt": "timestamp"
        }
      },
      "updateComment": {
        "method": "PUT",
        "url": "/api/posts/{id}/comments/{commentId}",
        "auth": "required",
        "ownerOnly": true,
        "body": {
          "content": "string"
        }
      },
      "deleteComment": {
        "method": "DELETE", 
        "url": "/api/posts/{id}/comments/{commentId}",
        "auth": "required",
        "ownerOnly": true
      }
    },
    "account": {
      "getAccount": {
        "method": "GET", 
        "url": "/api/account",
        "auth": "required"
      },
      "updateAccount": {
        "method": "PUT",
        "url": "/api/account",
        "auth": "required",
        "body": {
          "username": "string",
          "email": "string"
        }
      },
      "deleteAccount": {
        "method": "DELETE", 
        "url": "/api/account",
        "auth": "required"
      }
    }
  }
}