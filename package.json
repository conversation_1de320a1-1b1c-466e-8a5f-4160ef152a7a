{"name": "app-starter-project-with-webpack", "version": "1.0.0", "description": "", "scripts": {"build": "webpack --config webpack.prod.js", "start-dev": "webpack serve --config webpack.dev.js", "serve": "http-server dist -p 9000", "start": "http-server dist -p 9000"}, "keywords": [], "author": "YOUR_NAME <<EMAIL>>", "license": "ISC", "devDependencies": {"@babel/preset-env": "^7.26.9", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "http-server": "^14.1.1", "mini-css-extract-plugin": "^2.9.2", "style-loader": "^4.0.0", "webpack": "^5.98.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0", "webpack-merge": "^6.0.1"}, "dependencies": {"idb": "^8.0.3", "ol": "^10.5.0", "sweetalert2": "^11.22.0"}}