import CONFIG from "../config.js";
import authService from "./auth-service.js";
import { retryWithBackoff, apiRequestQueue } from "../utils/index.js";

const createHeaders = (includeAuth = false) => {
  const headers = new Headers({
    "Content-Type": "application/json",
  });

  if (includeAuth) {
    const token = authService.getToken();
    console.log("Token untuk Authorization:", token);
    if (token) {
      headers.append("Authorization", `Bearer ${token}`);
    }
  }

  return headers;
};

export async function apiRequest(url, options = {}) {
  const makeRequest = async () => {
    try {
      const response = await fetch(url, options);

      // Handle different response types
      let data;
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        data = text ? { message: text } : {};
      }

      if (!response.ok) {
        // Create more specific error messages for different status codes
        let errorMessage = data.message || `API request failed with status ${response.status}`;

        switch (response.status) {
          case 429:
            errorMessage = "Terlalu banyak permintaan. Silakan tunggu sebentar dan coba lagi.";
            break;
          case 500:
            errorMessage = "Terjadi kesalahan pada server. Silakan coba lagi.";
            break;
          case 503:
            errorMessage = "Server sedang tidak tersedia. Silakan coba lagi nanti.";
            break;
        }

        const error = new Error(errorMessage);
        error.status = response.status;
        error.response = response;
        throw error;
      }

      return data;
    } catch (error) {
      // If it's a network error, add more context
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        const networkError = new Error('Tidak dapat terhubung ke server. Periksa koneksi internet Anda.');
        networkError.originalError = error;
        throw networkError;
      }
      throw error;
    }
  };

  // Use retry logic with exponential backoff
  return retryWithBackoff(makeRequest, 3, 1000, 10000);
}

export async function getData(endpoint, requiresAuth = false) {
  return apiRequestQueue.add(async () => {
    const url = `${CONFIG.BASE_URL}${endpoint}`;
    const options = {
      method: "GET",
      headers: createHeaders(requiresAuth),
    };

    return apiRequest(url, options);
  });
}

export async function postData(endpoint, data, requiresAuth = false) {
  return apiRequestQueue.add(async () => {
    const url = `${CONFIG.BASE_URL}${endpoint}`;
    const options = {
      method: "POST",
      headers: createHeaders(requiresAuth),
      body: JSON.stringify(data),
    };

    return apiRequest(url, options);
  });
}

export async function updateData(endpoint, data, requiresAuth = true) {
  return apiRequestQueue.add(async () => {
    const url = `${CONFIG.BASE_URL}${endpoint}`;
    const options = {
      method: "PUT",
      headers: createHeaders(requiresAuth),
      body: JSON.stringify(data),
    };

    return apiRequest(url, options);
  });
}

export async function deleteData(endpoint, requiresAuth = true) {
  return apiRequestQueue.add(async () => {
    const url = `${CONFIG.BASE_URL}${endpoint}`;
    const options = {
      method: "DELETE",
      headers: createHeaders(requiresAuth),
    };

    return apiRequest(url, options);
  });
}

export async function uploadFile(endpoint, formData, requiresAuth = true) {
  return apiRequestQueue.add(async () => {
    const url = `${CONFIG.BASE_URL}${endpoint}`;

    // Create headers without Content-Type for FormData (browser will set it with boundary)
    const headers = new Headers();

    if (requiresAuth) {
      const token = authService.getToken();
      if (token) {
        headers.append("Authorization", `Bearer ${token}`);
      }
    }

    const options = {
      method: "POST",
      headers: headers,
      body: formData,
    };

    return apiRequest(url, options);
  });
}
