import { NavigationBar } from "../../components/NavigationBar.js";
import ChatbotPresenter from "./ChatBotPage-Presenter.js";
import avatarService from "../../services/avatar-service.js";

export default class ChatbotPage {
  async render() {
    const userDisplayInfo = await avatarService.getUserDisplayInfo();

    const navbar = new NavigationBar({
      currentPath: window.location.hash.slice(1),
      userInitial: userDisplayInfo.initial,
      showProfile: true,
    });

return `
  <div class="chatbot-fullscreen-container">
    ${await navbar.render()}
    <section class="chatbot-wrapper">
      <h1 class="chatbot-welcome typing">
  <span class="text">Hai, selamat datang di ChatBot AgriEdu!</span><span class="cursor"></span>
</h1>
<p class="chatbot-desc typing-desc">
  <span class="text">Tanyakan apa saja pada ChatBot AgriEdu perihal tanamanmu</span><span class="cursor"></span>
</p>
<hr/>

      <div id="chat-container" class="chat-container"></div>
      <div class="input-container">
        <input type="text" id="chat-input" placeholder="Ketik pesan..." autocomplete="off" />
        <button id="send-btn">Kirim</button>
      </div>
    </section>
  </div>
`;
  }

  async afterRender() {
    // Initialize avatar service for this page
    avatarService.initializeAvatars();

    this.setupNavigationEvents();

    const chatContainer = document.getElementById('chat-container');
    const chatInput = document.getElementById('chat-input');
    const sendBtn = document.getElementById('send-btn');

    this.chatbot = new ChatbotPresenter(chatContainer, chatInput, sendBtn);
  }

  async setupNavigationEvents() {
    const userDisplayInfo = await avatarService.getUserDisplayInfo();

    const navbar = new NavigationBar({
      currentPath: window.location.hash.slice(1),
      userInitial: userDisplayInfo.initial,
      showProfile: true,
    });

    navbar.bindEvents();
  }
}

