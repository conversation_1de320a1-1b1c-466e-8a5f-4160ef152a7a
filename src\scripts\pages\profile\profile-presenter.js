import ProfileModel from './profile-model';
import authService from '../../data/auth-service';
import avatarService from '../../services/avatar-service.js';

const ProfilePresenter = {
async init(view) {
    this.view = view;

    try {
      await authService.getCurrentUser();
    } catch (error) {
      console.warn('Gagal update user data dari API:', error);
    }
    const profile = await ProfileModel.getUserProfile();
    view.showProfile(profile);

    this._setupEventListeners();
    this._setupAvatarUpload();
  },

_setupEventListeners() {
  const saveBtn = document.getElementById('saveProfileBtn');
  console.log('saveBtn:', saveBtn);
  if (!saveBtn) {
    console.error('Tombol simpan tidak ditemukan di DOM!');
    return;
  }
  saveBtn.addEventListener('click', () => this._handleSaveProfile());
},


_setupAvatarUpload() {
  // This method is called after the view is rendered
  // The actual avatar upload event listeners are set up in the ProfilePage afterRender method
  // We just need to store a reference to handle the upload when triggered
  this.pendingAvatarFile = null;
},

async handleAvatarUpload(file) {
  if (!file) return;

  try {
    console.log('Uploading avatar file:', file.name);
    const response = await ProfileModel.uploadProfilePicture(file);
    console.log('Avatar upload successful:', response);

    // Update the profile display with new avatar
    const profile = await ProfileModel.getUserProfile();
    this.view.showProfile(profile);

    // The avatar service will automatically update all components via events
    // No need to manually refresh navigation as it listens to avatar updates

    // Return response with any additional message
    return {
      ...response,
      isLocalFallback: response.message && response.message.includes('sementara')
    };
  } catch (error) {
    console.error('Error uploading avatar:', error);
    throw error;
  }
},

async _handleSaveProfile() {
  console.log('Klik simpan terdeteksi');
  const fullName = document.getElementById('fullNameInput').value.trim();
  const username = document.getElementById('usernameInput').value.trim();
  const experienceRadios = document.querySelectorAll('input[name="experience"]');
  let experience = '';
  experienceRadios.forEach(radio => {
    if (radio.checked) experience = radio.value;
  });
  console.log({ fullName, username, experience });

  try {
    console.log('Memulai updateUsername');
    await ProfileModel.updateUsername(username);
    console.log('updateUsername selesai');

    console.log('Memulai saveSetupData');
    await ProfileModel.saveSetupData({ name: fullName, experience });
    console.log('saveSetupData selesai');

    // Handle pending avatar upload if any
    if (this.pendingAvatarFile) {
      console.log('Uploading pending avatar file');
      try {
        await this.handleAvatarUpload(this.pendingAvatarFile);
        this.pendingAvatarFile = null;
        console.log('Avatar upload completed');
      } catch (avatarError) {
        console.error('Avatar upload failed:', avatarError);
        alert('Profil disimpan, tetapi gagal mengupload foto profil: ' + avatarError.message);
        return;
      }
    }

    // Refresh navigation to update user display
    if (this.view.refreshNavigation) {
      await this.view.refreshNavigation();
    }

    alert('Profil berhasil disimpan!');
  } catch (error) {
    console.error('Error:', error);
    alert('Gagal menyimpan profil: ' + error.message);
  }
}

};

export default ProfilePresenter;
