/**
 * Avatar System Test
 * This file contains tests to verify the global avatar system functionality
 */

import avatarService from '../services/avatar-service.js';
import authService from '../data/auth-service.js';

class AvatarSystemTest {
  constructor() {
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 Starting Avatar System Tests...');
    
    try {
      await this.testAvatarServiceInitialization();
      await this.testDefaultAvatarFallback();
      await this.testUserDisplayInfo();
      await this.testAvatarUpdate();
      await this.testGlobalEventSystem();
      await this.testErrorHandling();
      
      this.displayResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async testAvatarServiceInitialization() {
    console.log('🔍 Testing Avatar Service Initialization...');
    
    try {
      // Test that avatar service is properly initialized
      const defaultAvatar = avatarService.getDefaultAvatarUrl();
      this.assert(defaultAvatar === 'images/avatar.jpg', 'Default avatar URL should be correct');
      
      // Test that getCurrentAvatarUrl returns a valid URL
      const currentAvatar = avatarService.getCurrentAvatarUrl();
      this.assert(typeof currentAvatar === 'string', 'Current avatar should return a string');
      this.assert(currentAvatar.length > 0, 'Current avatar should not be empty');
      
      this.testResults.push({ test: 'Avatar Service Initialization', status: 'PASS' });
    } catch (error) {
      this.testResults.push({ test: 'Avatar Service Initialization', status: 'FAIL', error: error.message });
    }
  }

  async testDefaultAvatarFallback() {
    console.log('🔍 Testing Default Avatar Fallback...');
    
    try {
      // Clear any existing avatar data
      localStorage.removeItem('user_avatar_local');
      authService.clearAuthData();
      
      // Test that we get default avatar when no custom avatar is set
      const avatarUrl = avatarService.getCurrentAvatarUrl();
      this.assert(avatarUrl === 'images/avatar.jpg', 'Should fallback to default avatar when no custom avatar is set');
      
      this.testResults.push({ test: 'Default Avatar Fallback', status: 'PASS' });
    } catch (error) {
      this.testResults.push({ test: 'Default Avatar Fallback', status: 'FAIL', error: error.message });
    }
  }

  async testUserDisplayInfo() {
    console.log('🔍 Testing User Display Info...');
    
    try {
      // Test getUserDisplayInfo returns proper structure
      const userInfo = await avatarService.getUserDisplayInfo();
      
      this.assert(typeof userInfo === 'object', 'User display info should be an object');
      this.assert(typeof userInfo.avatar === 'string', 'Avatar should be a string');
      this.assert(typeof userInfo.username === 'string', 'Username should be a string');
      this.assert(typeof userInfo.initial === 'string', 'Initial should be a string');
      this.assert(typeof userInfo.hasCustomAvatar === 'boolean', 'hasCustomAvatar should be a boolean');
      this.assert(userInfo.initial.length === 1, 'Initial should be exactly one character');
      
      this.testResults.push({ test: 'User Display Info', status: 'PASS' });
    } catch (error) {
      this.testResults.push({ test: 'User Display Info', status: 'FAIL', error: error.message });
    }
  }

  async testAvatarUpdate() {
    console.log('🔍 Testing Avatar Update...');
    
    try {
      const testAvatarUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      
      // Test avatar update
      avatarService.updateAvatar(testAvatarUrl);
      
      // Verify the avatar was updated
      const currentAvatar = avatarService.getCurrentAvatarUrl();
      this.assert(currentAvatar === testAvatarUrl, 'Avatar should be updated to new URL');
      
      // Verify it's stored in auth service
      const storedAvatar = authService.getUserAvatar();
      this.assert(storedAvatar === testAvatarUrl, 'Avatar should be stored in auth service');
      
      this.testResults.push({ test: 'Avatar Update', status: 'PASS' });
    } catch (error) {
      this.testResults.push({ test: 'Avatar Update', status: 'FAIL', error: error.message });
    }
  }

  async testGlobalEventSystem() {
    console.log('🔍 Testing Global Event System...');
    
    try {
      let eventFired = false;
      const testAvatarUrl = 'test-avatar-url.jpg';
      
      // Listen for avatar update event
      const eventListener = (event) => {
        if (event.detail.avatarUrl === testAvatarUrl) {
          eventFired = true;
        }
      };
      
      window.addEventListener('avatarUpdated', eventListener);
      
      // Trigger avatar update
      avatarService.updateAvatar(testAvatarUrl);
      
      // Wait a bit for event to fire
      await new Promise(resolve => setTimeout(resolve, 100));
      
      this.assert(eventFired, 'Avatar update event should be fired');
      
      // Clean up
      window.removeEventListener('avatarUpdated', eventListener);
      
      this.testResults.push({ test: 'Global Event System', status: 'PASS' });
    } catch (error) {
      this.testResults.push({ test: 'Global Event System', status: 'FAIL', error: error.message });
    }
  }

  async testErrorHandling() {
    console.log('🔍 Testing Error Handling...');
    
    try {
      // Test with invalid avatar URL
      avatarService.updateAvatar(null);
      const currentAvatar = avatarService.getCurrentAvatarUrl();
      this.assert(currentAvatar === 'images/avatar.jpg', 'Should fallback to default when null avatar is set');
      
      // Test with empty string
      avatarService.updateAvatar('');
      const currentAvatar2 = avatarService.getCurrentAvatarUrl();
      this.assert(currentAvatar2 === 'images/avatar.jpg', 'Should fallback to default when empty avatar is set');
      
      this.testResults.push({ test: 'Error Handling', status: 'PASS' });
    } catch (error) {
      this.testResults.push({ test: 'Error Handling', status: 'FAIL', error: error.message });
    }
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(`Assertion failed: ${message}`);
    }
  }

  displayResults() {
    console.log('\n📊 Avatar System Test Results:');
    console.log('================================');
    
    let passCount = 0;
    let failCount = 0;
    
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.test}: ${result.status}`);
      
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      if (result.status === 'PASS') passCount++;
      else failCount++;
    });
    
    console.log('================================');
    console.log(`Total: ${this.testResults.length} | Pass: ${passCount} | Fail: ${failCount}`);
    
    if (failCount === 0) {
      console.log('🎉 All tests passed! Avatar system is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the implementation.');
    }
  }
}

// Export for use in browser console
window.AvatarSystemTest = AvatarSystemTest;

// Auto-run tests if in development mode
if (window.location.hostname === 'localhost') {
  // Run tests after a short delay to ensure all modules are loaded
  setTimeout(async () => {
    const tester = new AvatarSystemTest();
    await tester.runAllTests();
  }, 2000);
}

export default AvatarSystemTest;
