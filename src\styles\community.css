/* Form Diskusi - Desktop Layout */
.container-form {
  max-width: 1300px;
  width: 100%;
  margin: 2rem auto;
  padding: 2.5rem 3rem;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  font-family: "Poppins", Tahoma, Geneva, Verdana, sans-serif;
  border: 1px solid #e0e0e0;
}

.container-form h1 {
  text-align: center;
  margin-bottom: 10px !important;
  color: #333;
  font-weight: 700;
}

.container-form hr {
  height: 2px;
  margin-top: 0px;
  margin-bottom: 15px;
}

input::placeholder,
textarea::placeholder {
  font-size: 0.8rem;
  color: #999;
}

* {
  font-family: "Poppins", sans-serif;
}

.container-form form > div {
  margin-bottom: 18px;
}

.container-form label {
  display: block;
  margin-bottom: 6px;
  color: #555;
  font-size: 0.87rem !important;
}

.container-form input[type="text"],
.container-form textarea,
.container-form input[type="file"] {
  width: 100%;
  padding: 10px 14px;
  border-bottom: 1.8px solid #ccc;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  resize: vertical;
}

.container-form input[type="text"]:focus,
.container-form textarea:focus {
  width: 100%;
  border-color: #242424;
  outline: none;
}

.container-form textarea {
  min-height: 120px;
}

.container-form button[type="submit"] {
  width: 100%;
  padding: 12px 0;
  background-color: #a9e652;
  border: none;
  border-radius: 8px;
  color: rgb(38, 38, 38);
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.container-form button[type="submit"]:hover {
  background-color: #89e00e;
}

#post-result {
  margin-top: 20px;
  font-size: 16px;
  color: #1f2937;
  text-align: center;
}

.file-camera-wrapper {
  display: flex;
  flex-direction: column;
}

.file-camera-wrapper .input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 5px;
}

.file-camera-wrapper .input-group input {
  flex: 1;
}

.file-camera-wrapper label {
  white-space: nowrap;
  margin-bottom: 0px;
}

#showCameraBtn {
  background: #eeeeee;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 10px 12px;
  border-radius: 10px;
}

#cameraSection {
  max-width: 100%; /* supaya gak melebihi container induk */
  overflow: hidden; /* sembunyikan yang kelewat */
  border: 1px solid #ccc; /* opsional, supaya kelihatan batasnya */
  padding: 8px;
  box-sizing: border-box;
  background: #fafafa;
  border-radius: 8px;
}

#cameraSection video,
#cameraSection canvas {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 4px;
  object-fit: contain;
  margin-bottom: 8px;
}

#cameraSection select,
#cameraSection button {
  max-width: 100%;
  box-sizing: border-box;
  margin-bottom: 8px;
}

#capturePhoto,
#stopCamera {
  display: block; /* supaya bisa diatur margin auto */
  margin: 0 auto 8px auto; /* atas 0, kanan & kiri auto, bawah 8px */
  width: 100%; /* samakan dengan select, bisa disesuaikan */
  text-align: center; /* biar teks tombol juga di tengah */
}

#capturePhoto {
  background-color: rgb(210, 210, 210);
  color: rgb(39, 39, 39);
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
}

#retakePhoto {
  background-color: black;
  color: white;
  border: none;
  padding: 5px 8px;
  cursor: pointer;
  border-radius: 4px;
  text-align: center;
  display: inline-block;
}

#stopCamera {
  background-color: rgb(246, 63, 63);
  color: white;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  margin-left: 0px !important;
}

/* Profile Container Layout */
.profile-container {
  display: flex;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  align-items: flex-start;
}

.profile-sidebar {
  flex: 0 0 250px;
  width: 280px;
  min-width: 280px;
  background-color: #ffffff;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: fit-content;
  position: sticky;
  top: 2rem;
  border: 1px solid #e9ecef;
}

main {
  flex: 1;
}

.sidebar-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 1.5rem;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sidebar-avatar p {
  margin: 0.5rem 0;
  font-weight: 600;
  color: #333;
}

.sidebar-avatar p:first-of-type {
  font-size: 1.2rem;
  color: #2c3e50;
}

.sidebar-avatar p:last-of-type {
  font-size: 0.9rem;
  color: #7f8c8d;
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #e9ecef;
}

.sidebar-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: #a9e652;
  color: #292929;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgb(137, 224, 14);
}

.sidebar-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px #89e00e;
  background: #89e00e;
}

/* Community Content */
.community-content {
  flex: 1;
  min-width: 0;
}

.community-banner {
  padding: 2rem 7rem;
  max-width: 100% !important;
  background: #f5f5f5;
  border: 1px solid #bdbdbd;
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.banner-icon {
  width: 70px;
  height: 70px;
  object-fit: contain;
  flex-shrink: 0;
}

.banner-text {
  color: rgb(32, 32, 32);
  max-width: 50%;
}

.banner-text h2 {
  margin: 0 0;
}

/* Community Page Styles */
.community-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 0 1rem;
}

.community-header h1 {
  color: #333;
  margin: 0;
}

.create-post-btn {
  background-color: #a9e652;
  color: #333;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.create-post-btn:hover {
  background-color: #89e00e;
}

.posts-container {
  padding: 0;
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
  max-width: 100%;
}

.loading-posts {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.no-posts {
  text-align: center;
  padding: 3rem 2rem;
  background: #f9f9f9;
  border-radius: 12px;
  border: 2px dashed #ddd;
}

.no-posts p {
  color: #666;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.create-first-post-btn {
  background-color: #a9e652;
  color: #333;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.create-first-post-btn:hover {
  background-color: #89e00e;
}

.post-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.post-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
}

.post-header {
  padding: 1rem 1.5rem 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.post-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #a9e652;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.author-info h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.post-date {
  color: #666;
  font-size: 0.85rem;
}

.post-content {
  padding: 0.5rem 1.5rem 1rem;
}

.post-content h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.post-content p {
  margin: 0 0 1rem 0;
  color: #555;
  line-height: 1.6;
}

.post-image {
  width: 60%;
  max-width: 400px;
  max-height: 180px;
  object-fit: cover;
  border-radius: 8px;
  margin: 0.5rem auto 0;
  display: block;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.post-image:hover {
  transform: scale(1.02);
}

.post-actions {
  padding: 0.75rem 1.5rem 1rem;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
  align-items: center;
}

.action-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.action-btn:hover {
  background-color: #f8f9fa;
  color: #333;
  transform: translateY(-1px);
}

.comment-btn:hover {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.view-btn:hover {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

.delete-post-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.delete-post-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #c82333;
  transform: scale(1.1);
}

.post-image-container {
  margin: 1rem auto 0;
  border-radius: 12px;
  overflow: hidden;
  max-height: 180px;
  max-width: 400px;
  width: 60%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.post-image-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.post-image-container img {
  width: 100%;
  height: auto;
  max-height: 180px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.post-image-container:hover img {
  transform: scale(1.05);
}

/* Modal Styles - Full Screen Design */
.post-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(5px);
  }
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  cursor: pointer;
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 20px;
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  z-index: 1001;
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(50px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close {
  background: rgba(220, 53, 69, 0.1);
  border: none;
  font-size: 1.2rem;
  color: #dc3545;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background-color: #dc3545;
  color: white;
  transform: rotate(90deg) scale(1.1);
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
  background: #ffffff;
}

.post-detail-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.post-detail-header .author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #a9e652;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #333;
  font-size: 1.2rem;
}

.post-detail-header .author-info h4 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.post-detail-header .post-date {
  color: #666;
  font-size: 0.9rem;
}

.post-detail-content {
  background: #ffffff;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.post-detail-content h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 1.3;
  padding-bottom: 0.5rem;
}

.post-detail-content p {
  margin: 0 0 1.5rem 0;
  color: #34495e;
  line-height: 1.8;
  font-size: 1.1rem;
  text-align: justify;
}

.post-detail-image {
  margin: 2rem 0;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

.post-detail-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(169, 230, 82, 0.1) 50%,
    transparent 100%
  );
  pointer-events: none;
}

.post-detail-image img {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: contain;
  display: block;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.post-detail-image:hover img {
  transform: scale(1.02);
}

/* Comments Styles - Enhanced Design */
.post-summary {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.post-summary h3 {
  margin: 0.5rem 0 1rem;
  font-size: 1.4rem;
  color: #2c3e50;
  font-weight: 700;
}

.post-summary p {
  margin: 0;
  color: #34495e;
  line-height: 1.7;
  font-size: 1rem;
}

.comments-section {
  background: #ffffff;
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.comments-section h4 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 700;
  padding-bottom: 0.5rem;
}

.add-comment {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  padding: 1.5rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.add-comment textarea {
  width: 100%;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1rem;
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  min-height: 100px;
}

.add-comment textarea:focus {
  outline: none;
  border-color: #a9e652;
  box-shadow: 0 0 0 3px rgba(169, 230, 82, 0.2);
  transform: translateY(-2px);
}

.add-comment-btn {
  background: linear-gradient(135deg, #a9e652 0%, #89e00e 100%);
  color: #333;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(169, 230, 82, 0.3);
}

.add-comment-btn:hover {
  background: linear-gradient(135deg, #89e00e 0%, #a9e652 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(169, 230, 82, 0.4);
}

.comments-list {
  max-height: 400px;
  overflow-y: auto;
}

.comment-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: box-shadow 0.3s ease;
}

.comment-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.author-avatar.small {
  width: 32px;
  height: 32px;
  font-size: 0.9rem;
}

.comment-author .author-info h5 {
  margin: 0;
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
}

.comment-date {
  color: #666;
  font-size: 0.8rem;
}

.comment-content p {
  margin: 0;
  color: #555;
  line-height: 1.5;
  font-size: 0.9rem;
}

.delete-comment-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.delete-comment-btn:hover {
  background-color: #fee;
  color: #c82333;
}

.no-comments {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 1rem 0;
}

.post-actions-detail {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  color: #c33;
}

.error-message button {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.error-message button:hover {
  background-color: #c0392b;
}

/* Delete Button Styles */
.delete-btn {
  background-color: #dc3545 !important;
  color: white !important;
}

.delete-btn:hover {
  background-color: #c82333 !important;
  color: white !important;
  transform: translateY(-2px);
}

.delete-comment-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 5px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.delete-comment-btn:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

/* Responsive Design for Community Layout */
@media (max-width: 1024px) {
  .profile-container {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
  }

  .profile-sidebar {
    width: 100%;
    min-width: auto;
    position: static;
    padding: 1.5rem;
  }

  .sidebar-avatar {
    flex-direction: row;
    text-align: left;
    gap: 1rem;
  }

  .avatar {
    width: 80px;
    height: 80px;
    margin-bottom: 0;
  }

  .sidebar-avatar p {
    margin: 0.25rem 0;
  }

  .sidebar-button {
    margin-left: auto;
    width: auto;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .modal-content {
    max-width: 95%;
    margin: 1rem;
  }

  .modal-header {
    padding: 1.5rem;
  }

  .modal-body {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 0.5rem;
    gap: 1rem;
  }

  .profile-sidebar {
    padding: 1rem;
    border-radius: 12px;
  }

  .sidebar-avatar {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .avatar {
    width: 60px;
    height: 60px;
  }

  .sidebar-button {
    width: 100%;
    margin-left: 0;
    margin-top: 1rem;
  }

  .community-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .posts-container {
    gap: 1.5rem;
  }

  .container-form {
    max-width: 95%;
    width: 95%;
    margin: 1rem auto;
    padding: 1.5rem;
  }

  .modal-content {
    width: 98%;
    max-height: 95vh;
    border-radius: 16px;
  }

  .modal-header h2 {
    font-size: 1.4rem;
  }

  .post-detail-content {
    padding: 1.5rem;
  }

  .post-detail-content h3 {
    font-size: 1.3rem;
  }

  .add-comment {
    padding: 1rem;
  }

  .comments-section {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: 0.25rem;
  }

  .posts-container {
    gap: 1rem;
  }

  .container-form {
    max-width: 98%;
    width: 98%;
    margin: 0.5rem auto;
    padding: 1rem;
  }

  .container-form h1 {
    font-size: 1.3rem;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }

  .post-detail-content {
    padding: 1rem;
  }

  .post-detail-content h3 {
    font-size: 1.2rem;
  }

  .post-detail-content p {
    font-size: 1rem;
  }

  .add-comment {
    padding: 0.75rem;
  }

  .add-comment textarea {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .add-comment-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .comments-section {
    padding: 1rem;
  }
}

/* Loading and Error States */
.loading-message {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  margin: 1rem 0;
}

.loading-message p {
  color: #666;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #a9e652;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 12px;
  margin: 1rem 0;
}

.error-message p {
  color: #c53030;
  margin-bottom: 1rem;
}

.error-message small {
  color: #718096;
  display: block;
  margin-bottom: 0.5rem;
}

.error-message button {
  background-color: #a9e652;
  color: #333;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
  margin: 0.5rem;
}

.error-message button:hover {
  background-color: #89e00e;
}

.retry-btn {
  background-color: #3182ce !important;
  color: white !important;
}

.retry-btn:hover {
  background-color: #2c5aa0 !important;
}

.rate-limit-notice {
  text-align: center;
  padding: 1.5rem;
  background: #fffbeb;
  border: 1px solid #fed7aa;
  border-radius: 12px;
  margin: 1rem 0;
}

.rate-limit-notice p {
  color: #92400e;
  margin-bottom: 0.5rem;
}

.rate-limit-notice p:first-child {
  font-weight: 600;
  font-size: 1.1rem;
}

.rate-limit-notice small {
  color: #a16207;
  font-style: italic;
}

/* Mobile responsive for loading and error states */
@media (max-width: 768px) {
  .loading-message,
  .error-message,
  .rate-limit-notice {
    margin: 1rem;
    padding: 1rem;
  }

  .loading-spinner {
    width: 30px;
    height: 30px;
  }
}
