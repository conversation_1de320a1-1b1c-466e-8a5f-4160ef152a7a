.app-navbar {
  background: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid rgb(211, 211, 211) !important;
}

.app-navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 10px;
  gap: 10px;
  position: relative;
  flex-wrap: wrap;
  box-sizing: border-box;
}

.app-logo {
  font-size: 22px;
  font-weight: bold;
  text-decoration: none;
  color: #333;
  display: flex;
  align-items: center;
}

.app-logo img {
  height: 46px;
  transition: transform 0.3s ease;
}

.app-logo:hover img {
  transform: scale(1.05);
}

.app-logo .green {
  color: #64b100;
}

.app-nav {
  display: grid;
  grid-auto-flow: column;
  justify-content: center;
  align-items: center;
  gap: 25px;
  margin: 0 auto;
  text-align: center;
}

.app-nav a:not(.app-logout) {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 8px 15px;
  white-space: nowrap;
}

.app-nav a:not(.app-logout):hover,
.app-nav a.active {
  color: #64b100;
}

.app-nav a.active {
  font-weight: 600;
}

.nav-link {
  color: #888;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #000;
}

.nav-link.active {
  color: #000;
  font-weight: 600;
  border-bottom: 2px solid #000;
}

.app-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.app-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.app-menu-toggle.active {
  color: #64b100;
}

.user-profile-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
  position: relative;
  justify-self: end;
}

.app-logout {
  color: #e74c3c;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-right: 10px;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.app-logout:hover {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.app-logout i {
  font-size: 14px;
  color: #e74c3c;
}

.app-profile-icon {
  width: 42px;
  height: 42px;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 2px solid #fff;
  margin-left: 5px;
  overflow: hidden;
  position: relative;
}

.app-profile-icon .profile-avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.app-profile-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
  .app-navbar-content {
    position: relative;
  }

  .app-nav {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    grid-template-columns: 1fr;
    text-align: center;
    background: #fff;
    padding: 20px 0;
    border-radius: 0 0 12px 12px;
    gap: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    margin-top: 10px;
  }

  .app-nav.show {
    display: flex !important;
    animation: slideDown 0.3s ease;
  }

  .app-menu-toggle {
    display: block;
  }

  .user-profile-container {
    position: absolute;
    top: 60px;
    right: 20px;
    flex-direction: column-reverse;
    background: #fff;
    padding: 10px;
    border-radius: 8px;
    gap: 8px;
    display: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }

  .app-nav.show ~ .user-profile-container {
    display: flex;
  }

  .app-logout {
    width: 100%;
    justify-content: center;
    order: 2;
  }

  .app-profile-icon {
    order: 1;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .app-navbar-content {
    position: relative;
  }

  .app-nav {
    grid-column: 1 / -1;
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    grid-template-columns: 1fr;
    text-align: center;
    background: #fff;
    padding: 16px 0;
    border-radius: 0 0 10px 10px;
    gap: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    margin-top: 8px;
  }

  .app-menu-toggle {
    display: block;
  }

  .user-profile-container {
    position: absolute;
    top: 56px;
    right: 16px;
    flex-direction: column-reverse;
    background: #fff;
    padding: 8px;
    border-radius: 6px;
    gap: 6px;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }

  .app-nav.show ~ .user-profile-container {
    display: flex;
  }

  .app-logout {
    width: 100%;
    justify-content: center;
    order: 2;
  }

  .app-profile-icon {
    order: 1;
    margin: 0 auto;
  }
}

/* Global Avatar Styles */
img[data-avatar="true"] {
  border-radius: 50%;
  object-fit: cover;
  transition: all 0.3s ease;
}

img[data-avatar="true"]:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Animation for mobile menu */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
